<template>
  <div>
    <h1 class="text-3xl font-bold mb-6">Publishers Area</h1>

    <!-- Search Form -->
    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-8 max-w-2xl">
      <h2 class="text-xl font-semibold mb-4">Find Your Articles</h2>
      <p class="mb-4 text-gray-600">Enter your email address to find all articles you've submitted.</p>

      <form @submit.prevent="searchArticles" class="mb-4">
        <div class="flex flex-col md:flex-row gap-4">
          <div class="flex-grow">
            <input
              type="email"
              v-model="searchEmail"
              placeholder="Enter your email address"
              class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              required
            >
          </div>
          <div>
            <button
              type="submit"
              class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full md:w-auto"
              :disabled="searching"
            >
              <span v-if="searching" class="flex items-center justify-center">
                <span class="animate-spin h-4 w-4 mr-2 border-b-2 border-white rounded-full"></span>
                Searching...
              </span>
              <span v-else>Search Articles</span>
            </button>
          </div>
        </div>
      </form>
    </div>

    <!-- Search Results -->
    <div v-if="hasSearched">
      <!-- Loading State -->
      <div v-if="searching" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>

      <!-- Error State -->
      <div v-else-if="searchError" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <p>{{ searchError }}</p>
      </div>

      <!-- No Results -->
      <div v-else-if="articles.length === 0" class="bg-gray-100 p-6 rounded-lg text-center">
        <p class="text-lg text-gray-700">No articles found for this email address.</p>
        <NuxtLink to="/submit" class="mt-4 inline-block text-blue-500 hover:underline">
          Submit a new article
        </NuxtLink>
      </div>

      <!-- Results List -->
      <div v-else>
        <h2 class="text-2xl font-semibold mb-4">Your Articles ({{ articles.length }})</h2>

        <div class="space-y-6">
          <div
            v-for="article in articles"
            :key="article.id"
            class="bg-white rounded-lg shadow-md overflow-hidden"
          >
            <div class="p-6">
              <div class="flex justify-between items-start">
                <h3 class="text-xl font-semibold mb-2 text-gray-900">{{ article.article_title }}</h3>
                <div>
                  <span
                    :class="{
                      'bg-green-100 text-green-800': article.approved === 1,
                      'bg-yellow-100 text-yellow-800': article.approved === 0
                    }"
                    class="px-2 py-1 rounded text-xs font-medium" >
                    {{ article.approved === 1 ? 'Approved' : 'Pending Review' }}
                  </span>

                  <span
                    :class="{
                      'bg-green-100 text-green-800': article.paid === 1,
                      'bg-gray-100 text-gray-800': article.paid === 0
                    }"
                    class="ml-2 px-2 py-1 rounded text-xs font-medium"
                  >
                    {{ article.paid === 1 ? 'Paid' : 'Free' }}
                  </span>
                </div>
              </div>

              <div class="text-sm text-gray-600 mb-2">
                <span>Category: {{ article.category_name || 'Uncategorized' }}</span>
                <span class="mx-2">•</span>
                <span>Submitted: {{ article.formatted_date }}</span>
              </div>

              <div class="prose max-w-none mb-4">
                <p>{{ truncateText(article.article_body, 200) }}</p>
              </div>

              <div v-if="article.approved === 1" class="mt-2">
                <a :href="`/article/${slugify(article.article_title)}-${article.id}`"
                  class="text-blue-600 hover:text-blue-800 font-medium">
                  View Published Article →
                </a>
              </div>

              <div v-if="article.approved === 0 && article.paid === 0" class="mt-4 pt-4 border-t border-gray-200">
                <p class="text-sm text-gray-700 mb-2">Want to expedite the review process?</p>
                <a
                  :href="`https://buy.stripe.com/eVa6p038J07l03S7sy?prefilled_email=${encodeURIComponent(article.email_address)}`"
                  target="_blank"
                  class="inline-block bg-pink-500 hover:bg-pink-600 text-white font-bold py-2 px-4 rounded transition duration-200 text-sm"
                >
                  Expedite Review - £15
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Search state
const searchEmail = ref('');
const articles = ref([]);
const searching = ref(false);
const searchError = ref(null);
const hasSearched = ref(false);

// Truncate text with ellipsis
const truncateText = (text, maxLength) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// Convert text to URL-friendly slug
const slugify = (text) => {
  return text
    .toString()
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')        // Replace spaces with -
    .replace(/&/g, '-and-')      // Replace & with 'and'
    .replace(/[^\w\-]+/g, '')     // Remove all non-word characters
    .replace(/\-\-+/g, '-');      // Replace multiple - with single -
};

// Search articles by email
const searchArticles = async () => {
  if (!searchEmail.value) return;

  searching.value = true;
  searchError.value = null;
  hasSearched.value = true;

  try {
    const response = await fetch(`/api/search-articles-by-email?email=${encodeURIComponent(searchEmail.value)}`);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to search articles');
    }

    const data = await response.json();
    articles.value = data.articles || [];
  } catch (err) {
    console.error('Error searching articles:', err);
    searchError.value = err.message || 'Failed to search articles. Please try again later.';
  } finally {
    searching.value = false;
  }
};
</script>