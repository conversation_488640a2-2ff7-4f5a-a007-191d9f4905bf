import { getConnection } from '../utils/database.js';

export default defineEventHandler(async (event) => {
  try {
    // Get database connection
    const connection = await getConnection();

    // Query to get categories
    const [rows] = await connection.execute('SELECT id, category_name, link FROM categories ORDER BY category_name');

    // Close the connection
    await connection.end();

    // Return categories
    return {
      categories: rows
    };
  } catch (error) {
    console.error('Error fetching categories:', error);

    // Return error with appropriate status code
    return {
      statusCode: 500,
      statusMessage: 'Internal Server Error',
      message: 'Failed to fetch categories'
    };
  }
});
