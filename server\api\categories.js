import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create a function to get database connection
const getConnection = async () => {
  try {
    // Parse the DATABASE_URL from .env
    const dbUrl = process.env.DATABASE_URL;

    // Extract connection details from the URL
    // Format: mariadb://admin:fM33Engl20Kl@************:3338/articlebase
    const regex = /mariadb:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/;
    const match = dbUrl.match(regex);

    if (!match) {
      throw new Error('Invalid DATABASE_URL format');
    }

    const [, user, password, host, port, database] = match;

    // Create connection
    return await mysql.createConnection({
      host,
      user,
      password,
      database,
      port: parseInt(port, 10)
    });
  } catch (error) {
    console.error('Database connection error:', error);
    throw error;
  }
};

export default defineEventHandler(async (event) => {
  try {
    // Get database connection
    const connection = await getConnection();

    // Query to get categories
    const [rows] = await connection.execute('SELECT id, category_name, link FROM categories ORDER BY category_name');

    // Close the connection
    await connection.end();

    // Return categories
    return {
      categories: rows
    };
  } catch (error) {
    console.error('Error fetching categories:', error);

    // Return error with appropriate status code
    return {
      statusCode: 500,
      statusMessage: 'Internal Server Error',
      message: 'Failed to fetch categories'
    };
  }
});
