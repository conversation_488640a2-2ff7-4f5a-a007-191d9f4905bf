import { getConnection } from '../utils/database.js';

export default defineEventHandler(async (event) => {
  try {
    // Get request body
    const body = await readBody(event);

    // Validate required fields
    const requiredFields = ['authorName', 'email', 'categoryId', 'articleTitle', 'articleBody'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return {
          statusCode: 400,
          statusMessage: 'Bad Request',
          message: `Missing required field: ${field}`
        };
      }
    }

    // Validate terms and conditions acceptance
    if (!body.acceptTerms) {
      return {
        statusCode: 400,
        statusMessage: 'Bad Request',
        message: 'You must accept the terms and conditions'
      };
    }

    // Get database connection
    const connection = await getConnection();

    // Insert article into database
    const [result] = await connection.execute(
      `INSERT INTO article (
        author_name,
        email_address,
        category_id,
        article_title,
        article_body,
        message,
        url,
        keyword,
        accept_tc,
        paid,
        approved
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0, 0)`,
      [
        body.authorName,
        body.email,
        body.categoryId,
        body.articleTitle,
        body.articleBody,
        body.message || '',
        body.url || '',
        body.keyword || '',
        body.acceptTerms ? 1 : 0
      ]
    );

    // Close the connection
    await connection.end();

    // Return success response
    return {
      statusCode: 201,
      statusMessage: 'Created',
      message: 'Article submitted successfully',
      articleId: result.insertId
    };
  } catch (error) {
    console.error('Error submitting article:', error);

    // Return error with appropriate status code
    return {
      statusCode: 500,
      statusMessage: 'Internal Server Error',
      message: 'Failed to submit article'
    };
  }
});
