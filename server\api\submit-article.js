import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create a function to get database connection
const getConnection = async () => {
  try {
    // Parse the DATABASE_URL from .env
    const dbUrl = process.env.DATABASE_URL;

    // Extract connection details from the URL
    // Format: mariadb://admin:fM33Engl20Kl@************:3338/articlebase
    const regex = /mariadb:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/;
    const match = dbUrl.match(regex);

    if (!match) {
      throw new Error('Invalid DATABASE_URL format');
    }

    const [, user, password, host, port, database] = match;

    // Create connection
    return await mysql.createConnection({
      host,
      user,
      password,
      database,
      port: parseInt(port, 10)
    });
  } catch (error) {
    console.error('Database connection error:', error);
    throw error;
  }
};

export default defineEventHandler(async (event) => {
  try {
    // Get request body
    const body = await readBody(event);

    // Validate required fields
    const requiredFields = ['authorName', 'email', 'categoryId', 'articleTitle', 'articleBody'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return {
          statusCode: 400,
          statusMessage: 'Bad Request',
          message: `Missing required field: ${field}`
        };
      }
    }

    // Validate terms and conditions acceptance
    if (!body.acceptTerms) {
      return {
        statusCode: 400,
        statusMessage: 'Bad Request',
        message: 'You must accept the terms and conditions'
      };
    }

    // Get database connection
    const connection = await getConnection();

    // Insert article into database
    const [result] = await connection.execute(
      `INSERT INTO article (
        author_name,
        email_address,
        category_id,
        article_title,
        article_body,
        message,
        url,
        keyword,
        accept_tc,
        paid,
        approved
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0, 0)`,
      [
        body.authorName,
        body.email,
        body.categoryId,
        body.articleTitle,
        body.articleBody,
        body.message || '',
        body.url || '',
        body.keyword || '',
        body.acceptTerms ? 1 : 0
      ]
    );

    // Close the connection
    await connection.end();

    // Return success response
    return {
      statusCode: 201,
      statusMessage: 'Created',
      message: 'Article submitted successfully',
      articleId: result.insertId
    };
  } catch (error) {
    console.error('Error submitting article:', error);

    // Return error with appropriate status code
    return {
      statusCode: 500,
      statusMessage: 'Internal Server Error',
      message: 'Failed to submit article'
    };
  }
});
