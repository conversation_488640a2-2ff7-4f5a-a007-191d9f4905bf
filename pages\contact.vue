<template>
  <div class="w-full">
    <h1 class="text-3xl font-bold mb-6">Contact Us</h1>

    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4 w-full mx-auto" style="max-width: 1200px;">
      <form @submit.prevent="submitForm">
        <!-- Full Name -->
        <div class="mb-4">
          <label class="block text-gray-700 text-sm font-bold mb-2" for="fullname">
            Full Name*
          </label>
          <input
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="fullname"
            type="text"
            v-model="formData.fullname"
            required
          >
        </div>

        <!-- Company -->
        <div class="mb-4">
          <label class="block text-gray-700 text-sm font-bold mb-2" for="company">
            Company
          </label>
          <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="company"
            type="text"
            v-model="formData.company">
        </div>

        <!-- Email -->
        <div class="mb-4">
          <label class="block text-gray-700 text-sm font-bold mb-2" for="email">
            Email*
          </label>
          <input
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="email"
            type="email"
            v-model="formData.email"
            required
          >
        </div>

        <!-- Subject -->
        <div class="mb-4">
          <label class="block text-gray-700 text-sm font-bold mb-2" for="subject">
            Subject*
          </label>
          <input
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="subject"
            type="text"
            v-model="formData.subject"
            required
          >
        </div>

        <!-- Message -->
        <div class="mb-6">
          <label class="block text-gray-700 text-sm font-bold mb-2" for="message">
            Message*
          </label>
          <textarea
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline h-32"
            id="message"
            v-model="formData.message"
            required
          ></textarea>
        </div>

        <!-- Submit Button -->
        <div class="flex items-center justify-between">
          <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            type="submit"
            :disabled="submitting"
          >
            <span v-if="submitting" class="flex items-center">
              <span class="animate-spin h-4 w-4 mr-2 border-b-2 border-white rounded-full"></span>
              Sending...
            </span>
            <span v-else>Send Message</span>
          </button>
          <p class="text-sm text-gray-600">* Required fields</p>
        </div>
      </form>

      <!-- Success Message -->
      <div v-if="formSubmitted" class="mt-4 p-4 bg-green-100 text-green-700 rounded">
        Thank you for your message! We will get back to you shortly.
      </div>
    </div>
  </div>
</template>

<script setup>
const formData = ref({
  fullname: '',
  company: '',
  email: '',
  subject: '',
  message: ''
});

const formSubmitted = ref(false);

const submitting = ref(false);

const submitForm = async () => {
  submitting.value = true;

  try {
    // Send the form data to the API endpoint
    const response = await fetch('/api/submit-contact', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData.value)
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || 'Failed to submit contact form');
    }

    // Show success message and reset form
    formSubmitted.value = true;
    formData.value = {
      fullname: '',
      company: '',
      email: '',
      subject: '',
      message: ''
    };

    // Hide success message after 5 seconds
    setTimeout(() => {
      formSubmitted.value = false;
    }, 5000);
  } catch (err) {
    console.error('Error submitting contact form:', err);
    alert(`There was an error submitting your message: ${err.message}. Please try again.`);
  } finally {
    submitting.value = false;
  }
};
</script>