import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create a function to get database connection
export const getConnection = async () => {
  try {
    const isInternal = process.env.INTERNAL === 'TRUE';
    
    if (isInternal) {
      // Use internal database configuration
      const host = process.env.INTERNAL_HOST;
      const port = parseInt(process.env.INTERNAL_PORT_CONTAINER, 10);
      const user = process.env.USER;
      const password = process.env.USER_PASSWORD;
      const database = process.env.DATABASE;
      
      if (!host || !port || !user || !password || !database) {
        throw new Error('Missing internal database configuration variables');
      }
      
      console.log('Connecting to internal database:', { host, port, user, database });
      
      return await mysql.createConnection({
        host,
        user,
        password,
        database,
        port
      });
    } else {
      // Use remote database URL
      const dbUrl = process.env.REMOTE_DATABASE_URL;
      
      if (!dbUrl) {
        throw new Error('REMOTE_DATABASE_URL is not defined');
      }
      
      // Extract connection details from the URL
      // Format: mariadb://user:password@host:port/database
      const regex = /mariadb:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/;
      const match = dbUrl.match(regex);
      
      if (!match) {
        throw new Error('Invalid REMOTE_DATABASE_URL format');
      }
      
      const [, user, password, host, port, database] = match;
      
      console.log('Connecting to remote database:', { host, port, user, database });
      
      return await mysql.createConnection({
        host,
        user,
        password,
        database,
        port: parseInt(port, 10)
      });
    }
  } catch (error) {
    console.error('Database connection error:', error);
    throw error;
  }
};
