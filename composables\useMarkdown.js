export default function useMarkdown() {
  const parseMarkdown = (text) => {
    if (!text) return '';
    
    let html = text;
    
    // Convert line breaks to proper HTML
    html = html.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
    
    // Headers - Process from most specific to least specific (longest patterns first)
    html = html.replace(/^###### (.+)$/gm, '<h5 class="text-base font-semibold mb-2 mt-3 text-gray-800">$1</h5>');
    html = html.replace(/^##### (.+)$/gm, '<h4 class="text-lg font-semibold mb-2 mt-4 text-gray-800">$1</h4>');
    html = html.replace(/^#### (.+)$/gm, '<h3 class="text-xl font-semibold mb-3 mt-5 text-gray-800">$1</h3>');
    html = html.replace(/^### (.+)$/gm, '<h1 class="text-3xl font-bold mb-4 mt-8 text-gray-900">$1</h1>');
    html = html.replace(/^## (.+)$/gm, '<h2 class="text-2xl font-semibold mb-3 mt-6 text-gray-800">$1</h2>');
    
    // Bold text - **text** = <strong> (process before italic to avoid conflicts)
    html = html.replace(/\*\*([^*\n]+?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>');
    
    // Process lists - handle bullet points that start with *
    // First, let's handle individual list items
    const lines = html.split('\n');
    const processedLines = [];
    let inList = false;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Check if this line is a list item (starts with * followed by space)
      if (line.match(/^\* (.+)/)) {
        let listContent = line.replace(/^\* (.+)/, '$1');

        // Process bold and other inline formatting in list items
        listContent = listContent.replace(/\*\*([^*\n]+?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>');

        // If we're not in a list, start one
        if (!inList) {
          processedLines.push('<ul class="list-disc list-inside mb-4 space-y-2 text-gray-700">');
          inList = true;
        }

        processedLines.push(`<li class="ml-4">${listContent}</li>`);
      } else {
        // If we were in a list and this line is not a list item, close the list
        if (inList) {
          processedLines.push('</ul>');
          inList = false;
        }
        
        // Add the regular line
        if (line.length > 0) {
          processedLines.push(line);
        } else {
          processedLines.push('');
        }
      }
    }
    
    // Close any open list at the end
    if (inList) {
      processedLines.push('</ul>');
    }
    
    html = processedLines.join('\n');
    
    // Links - [text](url) = <a href="url">text</a>
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 hover:text-blue-800 underline" target="_blank" rel="noopener noreferrer">$1</a>');
    
    // Code blocks - `code` = <code>
    html = html.replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono text-gray-800">$1</code>');
    
    // Blockquotes - > text = <blockquote>
    html = html.replace(/^> (.+)$/gm, '<blockquote class="border-l-4 border-gray-300 pl-4 italic text-gray-600 my-4">$1</blockquote>');
    
    // Horizontal rules - --- = <hr>
    html = html.replace(/^---$/gm, '<hr class="border-gray-300 my-6">');
    
    // Convert double line breaks to paragraphs
    html = html.replace(/\n\n+/g, '</p><p class="mb-4 text-gray-700 leading-relaxed">');
    
    // Wrap the content in paragraph tags if it doesn't start with a block element
    if (!html.match(/^<(h[1-6]|ul|ol|blockquote|hr|div)/)) {
      html = '<p class="mb-4 text-gray-700 leading-relaxed">' + html;
    }
    
    // Close the last paragraph if needed
    if (!html.match(/<\/(h[1-6]|ul|ol|blockquote|hr|div|p)>$/)) {
      html = html + '</p>';
    }
    
    // Clean up empty paragraphs
    html = html.replace(/<p[^>]*><\/p>/g, '');
    
    // Convert single line breaks to <br> within paragraphs
    html = html.replace(/\n(?!<)/g, '<br>');
    
    return html;
  };
  
  return {
    parseMarkdown
  };
}
