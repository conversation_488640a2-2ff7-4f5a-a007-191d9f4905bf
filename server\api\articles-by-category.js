import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create a function to get database connection
const getConnection = async () => {
  try {
    // Parse the DATABASE_URL from .env
    const dbUrl = process.env.DATABASE_URL;
    
    // Extract connection details from the URL
    // Format: mariadb://admin:fM33Engl20Kl@************:3338/articlebase
    const regex = /mariadb:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/;
    const match = dbUrl.match(regex);
    
    if (!match) {
      throw new Error('Invalid DATABASE_URL format');
    }
    
    const [, user, password, host, port, database] = match;
    
    // Create connection
    return await mysql.createConnection({
      host,
      user,
      password,
      database,
      port: parseInt(port, 10)
    });
  } catch (error) {
    console.error('Database connection error:', error);
    throw error;
  }
};

export default defineEventHandler(async (event) => {
  try {
    // Get category ID from query parameters
    const query = getQuery(event);
    const categoryId = query.categoryId;
    
    if (!categoryId) {
      return {
        statusCode: 400,
        statusMessage: 'Bad Request',
        message: 'Category ID is required'
      };
    }
    
    // Get database connection
    const connection = await getConnection();
    
    // Get category details
    const [categoryRows] = await connection.execute(
      'SELECT id, category_name FROM categories WHERE id = ?',
      [categoryId]
    );
    
    if (categoryRows.length === 0) {
      await connection.end();
      return {
        statusCode: 404,
        statusMessage: 'Not Found',
        message: 'Category not found'
      };
    }
    
    const category = categoryRows[0];
    
    // Get approved articles for this category
    const [articleRows] = await connection.execute(
      `SELECT 
        id, 
        author_name, 
        article_title, 
        article_body, 
        DATE_FORMAT(created_at, '%M %d, %Y') as formatted_date,
        created_at
      FROM 
        article 
      WHERE 
        category_id = ? AND approved = 1
      ORDER BY 
        created_at DESC`,
      [categoryId]
    );
    
    // Close the connection
    await connection.end();
    
    // Return category and articles
    return {
      category,
      articles: articleRows
    };
  } catch (error) {
    console.error('Error fetching articles by category:', error);
    
    // Return error with appropriate status code
    return {
      statusCode: 500,
      statusMessage: 'Internal Server Error',
      message: 'Failed to fetch articles'
    };
  }
});
