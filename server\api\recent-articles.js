import { getConnection } from '../utils/database.js';

export default defineEventHandler(async (event) => {
  try {
    // Get query parameters
    const query = getQuery(event);
    const limit = parseInt(query.limit) || 10; // Default to 10 articles
    
    // Get database connection
    const connection = await getConnection();
    
    // Get recent approved articles
    const [rows] = await connection.execute(
      `SELECT 
        a.id, 
        a.author_name, 
        a.article_title, 
        a.article_body,
        a.category_id,
        DATE_FORMAT(a.created_at, '%M %d, %Y') as formatted_date,
        a.created_at,
        c.category_name
      FROM 
        article a
      LEFT JOIN
        categories c ON a.category_id = c.id
      WHERE 
        a.approved = 1
      ORDER BY 
        a.created_at DESC
      LIMIT ?`,
      [limit]
    );
    
    // Close the connection
    await connection.end();
    
    // Return articles
    return {
      articles: rows
    };
  } catch (error) {
    console.error('Error fetching recent articles:', error);
    
    // Return error with appropriate status code
    return {
      statusCode: 500,
      statusMessage: 'Internal Server Error',
      message: 'Failed to fetch recent articles'
    };
  }
});
