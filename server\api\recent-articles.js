import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create a function to get database connection
const getConnection = async () => {
  try {
    // Parse the DATABASE_URL from .env
    const dbUrl = process.env.DATABASE_URL;
    
    // Extract connection details from the URL
    // Format: mariadb://admin:fM33Engl20Kl@************:3338/articlebase
    const regex = /mariadb:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/;
    const match = dbUrl.match(regex);
    
    if (!match) {
      throw new Error('Invalid DATABASE_URL format');
    }
    
    const [, user, password, host, port, database] = match;
    
    // Create connection
    return await mysql.createConnection({
      host,
      user,
      password,
      database,
      port: parseInt(port, 10)
    });
  } catch (error) {
    console.error('Database connection error:', error);
    throw error;
  }
};

export default defineEventHandler(async (event) => {
  try {
    // Get query parameters
    const query = getQuery(event);
    const limit = parseInt(query.limit) || 10; // Default to 10 articles
    
    // Get database connection
    const connection = await getConnection();
    
    // Get recent approved articles
    const [rows] = await connection.execute(
      `SELECT 
        a.id, 
        a.author_name, 
        a.article_title, 
        a.article_body,
        a.category_id,
        DATE_FORMAT(a.created_at, '%M %d, %Y') as formatted_date,
        a.created_at,
        c.category_name
      FROM 
        article a
      LEFT JOIN
        categories c ON a.category_id = c.id
      WHERE 
        a.approved = 1
      ORDER BY 
        a.created_at DESC
      LIMIT ?`,
      [limit]
    );
    
    // Close the connection
    await connection.end();
    
    // Return articles
    return {
      articles: rows
    };
  } catch (error) {
    console.error('Error fetching recent articles:', error);
    
    // Return error with appropriate status code
    return {
      statusCode: 500,
      statusMessage: 'Internal Server Error',
      message: 'Failed to fetch recent articles'
    };
  }
});
