<template>
  <div>
    <h1 class="text-4xl font-bold mb-6">Welcome to Techread</h1>
    <p class="text-lg text-gray-700 mb-8">Your source for quality articles and publications.</p>

    <div class="mt-10">
      <h2 class="text-2xl font-semibold mb-6">Browse Categories</h2>

      <!-- Loading State -->
      <div v-if="loading" class="flex justify-center items-center py-8">
        <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <p>{{ error }}</p>
        <button
          @click="fetchCategories"
          class="mt-2 text-sm text-blue-500 hover:underline" >
          Try again
        </button>
      </div>

      <!-- Categories Grid -->
      <div v-else class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        <div v-for="category in categories" :key="category.id" class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
          <NuxtLink :to="`/cat/${category.link}-${category.id}`" class="block p-4 hover:bg-gray-50">
            <h3 class="text-lg font-medium text-gray-900 hover:text-blue-600">{{ category.category_name }}</h3>
          </NuxtLink>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="!loading && !error && categories.length === 0" class="text-center py-8 text-gray-500">
        <p>No categories found.</p>
      </div>
    </div>
  </div>
</template>

<script setup>
// State for categories
const categories = ref([]);
const loading = ref(true);
const error = ref(null);

// Fetch categories from API
const fetchCategories = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await fetch('/api/categories');

    if (!response.ok) {
      throw new Error(`Error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (data.categories && Array.isArray(data.categories)) {
      categories.value = data.categories;
    } else {
      throw new Error('Invalid response format');
    }
  } catch (err) {
    console.error('Failed to fetch categories:', err);
    error.value = 'Failed to load categories. Please try again later.';
  } finally {
    loading.value = false;
  }
};

// Call fetchCategories when component is mounted
onMounted(fetchCategories);
</script>