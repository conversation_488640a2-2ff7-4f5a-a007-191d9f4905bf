<template>
  <div>
    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <p>{{ error }}</p>
      <button
        @click="fetchArticles"
        class="mt-2 text-sm text-blue-500 hover:underline"
      >
        Try again
      </button>
    </div>

    <!-- Content -->
    <div v-else>
      <h1 class="text-3xl font-bold mb-2">{{ category.category_name }}</h1>
      <p class="text-gray-600 mb-8">Showing {{ articles.length }} approved articles</p>

      <!-- No Articles State -->
      <div v-if="articles.length === 0" class="bg-gray-100 p-6 rounded-lg text-center">
        <p class="text-lg text-gray-700">No approved articles found in this category.</p>
        <NuxtLink to="/submit" class="mt-4 inline-block text-blue-500 hover:underline">
          Submit an article
        </NuxtLink>
      </div>

      <!-- Articles List -->
      <div v-else class="space-y-8">
        <article
          v-for="article in articles"
          :key="article.id"
          class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
        >
          <div class="p-6">
            <h2 class="text-2xl font-semibold mb-2 text-gray-900">
              {{ article.article_title }}
            </h2>
            <div class="flex items-center text-sm text-gray-600 mb-4">
              <span>By {{ article.author_name }}</span>
              <span class="mx-2">•</span>
              <span>{{ article.formatted_date }}</span>
            </div>
            <div class="prose max-w-none">
              <div v-html="truncateAndStripHTML(article.article_body, 300)" class="article-preview"></div>
            </div>
            <div class="mt-4">
              <NuxtLink
                :to="`/article/${slugify(article.article_title)}-${article.id}`"
                class="text-blue-600 hover:text-blue-800 font-medium"
              >
                Read more →
              </NuxtLink>
            </div>
          </div>
        </article>
      </div>
    </div>
  </div>
</template>

<script setup>
const route = useRoute();
const loading = ref(true);
const error = ref(null);
const category = ref({});
const articles = ref([]);

// Get category ID from the URL
const getCategoryId = () => {
  // The slug parameter will be an array
  const slug = route.params.slug;

  if (!slug || slug.length === 0) {
    return null;
  }

  // Extract the category ID from the URL format: affiliate-programs-11
  // The ID is the number after the last hyphen
  const urlPart = slug[0];
  const matches = urlPart.match(/-([0-9]+)$/);

  if (matches && matches[1]) {
    return matches[1]; // Return the extracted ID
  }

  // Fallback: try to use the whole slug as ID if it's numeric
  return isNaN(Number(urlPart)) ? null : urlPart;
};

// Truncate and strip HTML for preview
const truncateAndStripHTML = (html, maxLength) => {
  if (!html) return '';

  // Create temporary element to handle HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;

  // Get text content
  const textContent = tempDiv.textContent || tempDiv.innerText || '';

  // Truncate text if needed
  let truncated = textContent;
  if (textContent.length > maxLength) {
    truncated = textContent.substring(0, maxLength) + '...';
  }

  // Return plain text for preview
  return truncated;
};

// Convert text to URL-friendly slug
const slugify = (text) => {
  return text
    .toString()
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')        // Replace spaces with -
    .replace(/&/g, '-and-')      // Replace & with 'and'
    .replace(/[^\w\-]+/g, '')     // Remove all non-word characters
    .replace(/\-\-+/g, '-');      // Replace multiple - with single -
};

// Fetch articles for the category
const fetchArticles = async () => {
  loading.value = true;
  error.value = null;

  try {
    const categoryId = getCategoryId();

    if (!categoryId) {
      throw new Error('Invalid category ID');
    }

    const response = await fetch(`/api/articles-by-category?categoryId=${categoryId}`);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch articles');
    }

    const data = await response.json();

    category.value = data.category;
    articles.value = data.articles;
  } catch (err) {
    console.error('Error fetching articles:', err);
    error.value = err.message || 'Failed to load articles. Please try again later.';
  } finally {
    loading.value = false;
  }
};

// Call fetchArticles when component is mounted
onMounted(fetchArticles);

// Watch for route changes to refetch articles
watch(() => route.params.slug, fetchArticles);
</script>

<style scoped>
.article-preview {
  line-height: 1.6;
}
</style>