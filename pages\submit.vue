<template>
  <div class="w-full">
    <h1 class="text-3xl font-bold mb-6">Submit Article</h1>

    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4 w-full mx-auto">
      <h2 class="text-2xl font-semibold mb-6">Submit Your Article</h2>

      <div v-if="loading" class="flex justify-center items-center py-4">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>

      <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <p>{{ error }}</p>
        <button
          @click="fetchCategories"
          class="mt-2 text-sm text-blue-500 hover:underline"
        >
          Try again
        </button>
      </div>

      <form v-else @submit.prevent="submitForm">
        <!-- Author Name -->
        <div class="mb-4">
          <label class="block text-gray-700 text-sm font-bold mb-2" for="authorName">
            Author name
          </label>
          <input
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="authorName"
            type="text"
            v-model="formData.authorName"
            required
          >
        </div>

        <!-- Email Address -->
        <div class="mb-4">
          <label class="block text-gray-700 text-sm font-bold mb-2" for="email">
            Email address
          </label>
          <input
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="email"
            type="email"
            v-model="formData.email"
            required
          >
        </div>

        <!-- Category Dropdown -->
        <div class="mb-4">
          <label class="block text-gray-700 text-sm font-bold mb-2" for="category">
            Category
          </label>
          <div class="relative">
            <select
              class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              id="category"
              v-model="formData.categoryId"
              required
            >
              <option value="" disabled>Select a category</option>
              <option
                v-for="category in categories"
                :key="category.id"
                :value="category.id"
              >
                {{ category.category_name }}
              </option>
            </select>
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
              </svg>
            </div>
          </div>
        </div>

        <!-- Article Title -->
        <div class="mb-4">
          <label class="block text-gray-700 text-sm font-bold mb-2" for="articleTitle">
            Article title
          </label>
          <input
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="articleTitle"
            type="text"
            v-model="formData.articleTitle"
            required
          >
        </div>

        <!-- Article Body -->
        <div class="mb-4">
          <label class="block text-gray-700 text-sm font-bold mb-2" for="articleBody">
            Article body
          </label>
          <textarea
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline h-32"
            id="articleBody"
            v-model="formData.articleBody"
            required
          ></textarea>
        </div>

        <!-- Message -->
        <div class="mb-4">
          <label class="block text-gray-700 text-sm font-bold mb-2" for="message">
            Message
          </label>
          <textarea
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline h-24"
            id="message"
            v-model="formData.message"
          ></textarea>
        </div>

        <!-- URL -->
        <div class="mb-4">
          <label class="block text-gray-700 text-sm font-bold mb-2" for="url">
            URL (optional)
          </label>
          <input
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="url"
            type="url"
            placeholder="https://example.com"
            v-model="formData.url"
          >
          <p class="text-xs text-gray-500 mt-1">Add a URL to link from your article</p>
        </div>

        <!-- Keyword -->
        <div class="mb-4">
          <label class="block text-gray-700 text-sm font-bold mb-2" for="keyword">
            Keyword (optional)
          </label>
          <input
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="keyword"
            type="text"
            placeholder="Main keyword for your link"
            v-model="formData.keyword"
          >
          <p class="text-xs text-gray-500 mt-1">Add a keyword for your link text</p>
        </div>

        <!-- Challenge Question -->
        <div class="mb-4">
          <label class="block text-gray-700 text-sm font-bold mb-2" for="challenge">
            Challenge question: {{ challengeQuestion }}
          </label>
          <input
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="challenge"
            type="number"
            v-model="formData.challengeAnswer"
            required
          >
          <p v-if="showChallengeError" class="text-red-500 text-xs italic mt-1">Incorrect answer, please try again.</p>
        </div>

        <!-- Terms Checkbox -->
        <div class="mb-6">
          <label class="flex items-center">
            <input
              type="checkbox"
              class="mr-2 leading-tight"
              v-model="formData.acceptTerms"
              required
            >
            <span class="text-sm">
              I accept and agree to Techread's
              <NuxtLink to="/terms" class="text-blue-500 hover:underline">Terms and Conditions</NuxtLink>
            </span>
          </label>
          <p v-if="termsError" class="text-red-500 text-xs italic mt-1">You must accept the terms and conditions to continue.</p>
        </div>

        <!-- Submit Button -->
        <div class="flex items-center justify-between">
          <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            type="submit"
            :disabled="submitting" >
            <span v-if="submitting" class="flex items-center">
              <span class="animate-spin h-4 w-4 mr-2 border-b-2 border-white rounded-full"></span>
              Submitting...
            </span>
            <span v-else>Submit Article</span>
          </button>
        </div>
      </form>

      <!-- Success Message -->
      <div v-if="formSubmitted" class="mt-4 p-4 bg-green-100 text-green-700 rounded">
        Thank you for your submission! We will review your article shortly.
      </div>

      <!-- Expedited Review Offer -->
      <div v-if="formSubmitted" class="mt-4 p-4 bg-pink-100 text-pink-800 rounded border border-pink-300">
        <h3 class="font-bold text-lg mb-2">Speed up approval</h3>
        <p class="mb-3">Offer to pay £15 to expedite the review process and get your article approved faster.</p>
        <a :href="`https://buy.stripe.com/eVa6p038J07l03S7sy?prefilled_email=${submittedEmail}`"
          target="_blank"
          class="inline-block bg-pink-500 hover:bg-pink-600 text-white font-bold py-2 px-4 rounded transition duration-200">
          Expedite Review - £15
        </a>
        <p class="text-xs mt-2 text-pink-700">Product: Expedited Review</p>
      </div>
    </div>
  </div>
</template>

<script setup>
// State for form data
const formData = ref({
  authorName: '',
  email: '',
  categoryId: '',
  articleTitle: '',
  articleBody: '',
  message: '',
  url: '',
  keyword: '',
  challengeAnswer: null,
  acceptTerms: false
});

// State for UI
const formSubmitted = ref(false);
const showChallengeError = ref(false);
const loading = ref(true);
const error = ref(null);
const categories = ref([]);
const submitting = ref(false);
const submittedEmail = ref('');

// Generate random math challenge
const num1 = Math.floor(Math.random() * 10) + 1;
const num2 = Math.floor(Math.random() * 10) + 1;
const operation = Math.random() > 0.5 ? '*' : '+';
const challengeQuestion = `${num1} ${operation} ${num2}`;
const correctAnswer = operation === '*' ? num1 * num2 : num1 + num2;

// Fetch categories from API
const fetchCategories = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await fetch('/api/categories');

    if (!response.ok) {
      throw new Error(`Error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (data.categories && Array.isArray(data.categories)) {
      categories.value = data.categories;
    } else {
      throw new Error('Invalid response format');
    }
  } catch (err) {
    console.error('Failed to fetch categories:', err);
    error.value = 'Failed to load categories. Please try again later.';
  } finally {
    loading.value = false;
  }
};

// Call fetchCategories when component is mounted
onMounted(fetchCategories);

// Error message for terms and conditions
const termsError = ref(false);

// Form submission handler
const submitForm = async () => {
  // Reset error states
  showChallengeError.value = false;
  termsError.value = false;

  // Validate challenge question
  if (parseInt(formData.value.challengeAnswer) !== correctAnswer) {
    showChallengeError.value = true;
    return;
  }

  // Validate terms and conditions
  if (!formData.value.acceptTerms) {
    termsError.value = true;
    return;
  }

  submitting.value = true;

  try {
    // Simple formatting: replace newlines with <br> tags
    const formattedData = { ...formData.value };
    formattedData.articleBody = formattedData.articleBody.replace(/\n/g, '<br>');

    // Send the form data to the API endpoint
    const response = await fetch('/api/submit-article', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formattedData)
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || 'Failed to submit article');
    }

    // Save the email for the payment link
    submittedEmail.value = formData.value.email;

    // Show success message and reset form
    formSubmitted.value = true;
    formData.value = {
      authorName: '',
      email: '',
      categoryId: '',
      articleTitle: '',
      articleBody: '',
      message: '',
      url: '',
      keyword: '',
      challengeAnswer: null,
      acceptTerms: false
    };

    // Hide success message after 9999 seconds
    setTimeout(() => {
      formSubmitted.value = false;
    }, 19999);
  } catch (err) {
    console.error('Error submitting article:', err);
    alert(`There was an error submitting your article: ${err.message}. Please try again.`);
  } finally {
    submitting.value = false;
  }
};
</script>