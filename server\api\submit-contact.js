import { getConnection } from '../utils/database.js';

export default defineEventHandler(async (event) => {
  try {
    // Get request body
    const body = await readBody(event);
    
    // Validate required fields
    const requiredFields = ['fullname', 'email', 'subject', 'message'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return {
          statusCode: 400,
          statusMessage: 'Bad Request',
          message: `Missing required field: ${field}`
        };
      }
    }
    
    // Get database connection
    const connection = await getConnection();
    
    // Insert contact into database
    const [result] = await connection.execute(
      `INSERT INTO contact (
        full_name, 
        company, 
        email,
        subject, 
        message
      ) VALUES (?, ?, ?, ?, ?)`,
      [
        body.fullname,
        body.company || '',
        body.email,
        body.subject,
        body.message
      ]
    );
    
    // Close the connection
    await connection.end();
    
    // Return success response
    return {
      statusCode: 201,
      statusMessage: 'Created',
      message: 'Contact message submitted successfully',
      contactId: result.insertId
    };
  } catch (error) {
    console.error('Error submitting contact form:', error);
    
    // Return error with appropriate status code
    return {
      statusCode: 500,
      statusMessage: 'Internal Server Error',
      message: 'Failed to submit contact form'
    };
  }
});
