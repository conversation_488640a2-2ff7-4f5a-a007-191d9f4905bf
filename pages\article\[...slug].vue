<template>
  <div>
    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <p>{{ error }}</p>
      <NuxtLink to="/" class="mt-2 text-sm text-blue-500 hover:underline">
        Return to homepage
      </NuxtLink>
    </div>

    <!-- Article Content -->
    <article v-else class="max-w-4xl mx-auto">
      <header class="mb-8">
        <h1 class="text-3xl font-bold mb-3">{{ article.article_title }}</h1>

        <div class="flex flex-wrap items-center text-sm text-gray-600 mb-4">
          <span class="mr-4 pl-1">By {{ article.author_name }}</span>
          <span class="mr-4 pl-1">{{ article.formatted_date }}</span>
          <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">
            {{ article.category_name || 'Uncategorized' }}
          </span>
        </div>
      </header>

      <div class="max-w-none">
        <div v-html="formattedArticleBody" class="article-content markdown-content"></div>
      </div>

      <!-- Article Link (if URL and keyword exist) -->
      <div v-if="article.url && article.keyword" class="mt-8 pt-6 border-t border-gray-200">
        <p class="text-sm text-gray-600 mb-2">Related Resource:</p>
        <a
          :href="article.url"
          :rel="article.paid === 1 ? 'follow' : 'nofollow'"
          target="_blank"
          class="text-blue-600 hover:text-blue-800 font-medium"
        >
          {{ article.keyword }}
        </a>
      </div>

      <div class="mt-8 pt-6 border-t border-gray-200">
        <NuxtLink to="/" class="text-blue-600 hover:text-blue-800 font-medium">
          ← Back to all articles
        </NuxtLink>
      </div>
    </article>
  </div>
</template>

<script setup>
const route = useRoute();
const loading = ref(true);
const error = ref(null);
const article = ref({});
const { parseMarkdown } = useMarkdown();

// Get article ID from the URL
const getArticleId = () => {
  // The slug parameter will be an array
  const slug = route.params.slug;

  if (!slug || slug.length === 0) {
    return null;
  }

  // Extract the article ID from the URL format: article-title-123
  // The ID is the number after the last hyphen
  const urlPart = slug[0];
  const matches = urlPart.match(/-([0-9]+)$/);

  if (matches && matches[1]) {
    return matches[1]; // Return the extracted ID
  }

  // Fallback: try to use the whole slug as ID if it's numeric
  return isNaN(Number(urlPart)) ? null : urlPart;
};

// Get formatted article body with markdown parsing
const formattedArticleBody = computed(() => {
  if (!article.value || !article.value.article_body) return '';
  return parseMarkdown(article.value.article_body);
});

// Fetch article data
const fetchArticle = async () => {
  loading.value = true;
  error.value = null;

  try {
    const articleId = getArticleId();

    if (!articleId) {
      throw new Error('Invalid article ID');
    }

    const response = await fetch(`/api/article-by-id?id=${articleId}`);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch article');
    }

    const data = await response.json();

    if (!data.article) {
      throw new Error('Article not found');
    }

    article.value = data.article;
  } catch (err) {
    console.error('Error fetching article:', err);
    error.value = err.message || 'Failed to load article. Please try again later.';
  } finally {
    loading.value = false;
  }
};

// Call fetchArticle when component is mounted
onMounted(fetchArticle);

// Watch for route changes to refetch article
watch(() => route.params.slug, fetchArticle);
</script>

<style scoped>
.markdown-content {
  line-height: 1.7;
}

.markdown-content h1 {
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.markdown-content h2 {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.25rem;
}

.markdown-content ul {
  padding-left: 1rem;
}

.markdown-content li {
  margin-bottom: 0.5rem;
}

.markdown-content blockquote {
  background-color: #f9fafb;
  padding: 1rem;
  border-radius: 0.375rem;
}

.markdown-content code {
  font-size: 0.875rem;
}

.markdown-content a:hover {
  text-decoration: underline;
}

/* Ensure proper spacing between elements */
.markdown-content > * + * {
  margin-top: 1rem;
}

.markdown-content > h1 + *,
.markdown-content > h2 + *,
.markdown-content > h3 + *,
.markdown-content > h4 + *,
.markdown-content > h5 + * {
  margin-top: 0.5rem;
}
</style>
