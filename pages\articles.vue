<template>
  <div>
    <h1 class="text-3xl font-bold mb-6">Recent Articles</h1>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <p>{{ error }}</p>
      <button
        @click="fetchArticles"
        class="mt-2 text-sm text-blue-500 hover:underline"
      >
        Try again
      </button>
    </div>

    <!-- No Articles State -->
    <div v-else-if="articles.length === 0" class="bg-gray-100 p-6 rounded-lg text-center">
      <p class="text-lg text-gray-700">No articles found.</p>
      <NuxtLink to="/submit" class="mt-4 inline-block text-blue-500 hover:underline">
        Submit an article
      </NuxtLink>
    </div>

    <!-- Articles List -->
    <div v-else>
      <p class="text-gray-600 mb-8">Showing the latest {{ articles.length }} approved articles</p>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <article
          v-for="article in articles"
          :key="article.id"
          class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
        >
          <div class="p-6">
            <h2 class="text-xl font-semibold mb-2 text-gray-900">
              {{ article.article_title }}
            </h2>
            <div class="flex flex-wrap items-center text-sm text-gray-600 mb-4">
              <span class="mr-3">By {{ article.author_name }}</span>
              <span class="mr-3">{{ article.formatted_date }}</span>
              <span
                v-if="article.category_name"
                class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium mt-1"
              >
                {{ article.category_name }}
              </span>
            </div>
            <div class="prose max-w-none">
              <div v-html="truncateAndStripHTML(article.article_body, 150)" class="article-preview"></div>
            </div>
            <div class="mt-4">
              <NuxtLink
                :to="`/article/${slugify(article.article_title)}-${article.id}`"
                class="text-blue-600 hover:text-blue-800 font-medium"
              >
                Read more →
              </NuxtLink>
            </div>
          </div>
        </article>
      </div>

      <div class="mt-12 text-center">
        <NuxtLink
          to="/submit"
          class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-full inline-flex items-center"
        >
          <span>Submit Your Article</span>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup>
// State for articles
const articles = ref([]);
const loading = ref(true);
const error = ref(null);

// Truncate and strip HTML for preview
const truncateAndStripHTML = (html, maxLength) => {
  if (!html) return '';

  // Create temporary element to handle HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;

  // Get text content
  const textContent = tempDiv.textContent || tempDiv.innerText || '';

  // Truncate text if needed
  let truncated = textContent;
  if (textContent.length > maxLength) {
    truncated = textContent.substring(0, maxLength) + '...';
  }

  // Return plain text for preview
  return truncated;
};

// Convert text to URL-friendly slug
const slugify = (text) => {
  return text
    .toString()
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')        // Replace spaces with -
    .replace(/&/g, '-and-')      // Replace & with 'and'
    .replace(/[^\w\-]+/g, '')     // Remove all non-word characters
    .replace(/\-\-+/g, '-');      // Replace multiple - with single -
};

// Fetch recent articles
const fetchArticles = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await fetch('/api/recent-articles?limit=10');

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch articles');
    }

    const data = await response.json();
    articles.value = data.articles || [];
  } catch (err) {
    console.error('Error fetching articles:', err);
    error.value = err.message || 'Failed to load articles. Please try again later.';
  } finally {
    loading.value = false;
  }
};

// Call fetchArticles when component is mounted
onMounted(fetchArticles);
</script>

<style scoped>
.article-preview {
  line-height: 1.6;
}
</style>