import { getConnection } from '../utils/database.js';

export default defineEventHandler(async (event) => {
  try {
    // Get email from query parameters
    const query = getQuery(event);
    const email = query.email;
    
    if (!email) {
      return {
        statusCode: 400,
        statusMessage: 'Bad Request',
        message: 'Email address is required'
      };
    }
    
    // Get database connection
    const connection = await getConnection();
    
    // Get articles by email address
    const [rows] = await connection.execute(
      `SELECT 
        a.id, 
        a.author_name, 
        a.email_address,
        a.article_title, 
        a.article_body,
        a.message,
        a.paid,
        a.approved,
        DATE_FORMAT(a.created_at, '%M %d, %Y') as formatted_date,
        a.created_at,
        c.category_name
      FROM 
        article a
      LEFT JOIN
        categories c ON a.category_id = c.id
      WHERE 
        a.email_address = ?
      ORDER BY 
        a.created_at DESC`,
      [email]
    );
    
    // Close the connection
    await connection.end();
    
    // Return articles
    return {
      articles: rows
    };
  } catch (error) {
    console.error('Error searching articles by email:', error);
    
    // Return error with appropriate status code
    return {
      statusCode: 500,
      statusMessage: 'Internal Server Error',
      message: 'Failed to search articles'
    };
  }
});
